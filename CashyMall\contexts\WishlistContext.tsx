import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { checkWishlist, addToWishlist, removeFromWishlist } from '@/services/api/wishlist';

interface WishlistContextType {
  wishlistItems: Set<string>;
  isInWishlist: (productId: string) => boolean;
  toggleWishlist: (productId: string) => Promise<boolean>;
  addToWishlistGlobal: (productId: string) => Promise<boolean>;
  removeFromWishlistGlobal: (productId: string) => Promise<boolean>;
  refreshWishlistStatus: (productId: string) => Promise<boolean>;
}

const WishlistContext = createContext<WishlistContextType | undefined>(undefined);

interface WishlistProviderProps {
  children: ReactNode;
}

export function WishlistProvider({ children }: WishlistProviderProps) {
  const [wishlistItems, setWishlistItems] = useState<Set<string>>(new Set());

  const isInWishlist = (productId: string): boolean => {
    return wishlistItems.has(productId);
  };

  const refreshWishlistStatus = async (productId: string): Promise<boolean> => {
    try {
      console.log(`WishlistContext: Checking wishlist status for product ${productId}`);
      const result = await checkWishlist(productId);
      
      let isInList = false;
      if (result && result.error === null && result.data !== null) {
        isInList = !!result.data;
      }

      console.log(`WishlistContext: Product ${productId} wishlist status: ${isInList}`);
      
      setWishlistItems(prev => {
        const newSet = new Set(prev);
        if (isInList) {
          newSet.add(productId);
        } else {
          newSet.delete(productId);
        }
        return newSet;
      });

      return isInList;
    } catch (error) {
      console.error(`WishlistContext: Failed to check wishlist status for product ${productId}:`, error);
      return false;
    }
  };

  const addToWishlistGlobal = async (productId: string): Promise<boolean> => {
    try {
      console.log(`WishlistContext: Adding product ${productId} to wishlist`);
      const response = await addToWishlist(productId);
      console.log(`WishlistContext: Add response:`, response);

      // Verify the status after adding
      const actualStatus = await refreshWishlistStatus(productId);
      return actualStatus;
    } catch (error) {
      console.error(`WishlistContext: Failed to add product ${productId} to wishlist:`, error);
      return false;
    }
  };

  const removeFromWishlistGlobal = async (productId: string): Promise<boolean> => {
    try {
      console.log(`WishlistContext: Removing product ${productId} from wishlist`);
      const response = await removeFromWishlist(productId);
      console.log(`WishlistContext: Remove response:`, response);

      // Verify the status after removing
      const actualStatus = await refreshWishlistStatus(productId);
      return actualStatus;
    } catch (error) {
      console.error(`WishlistContext: Failed to remove product ${productId} from wishlist:`, error);
      return true; // Assume it's still in wishlist on error
    }
  };

  const toggleWishlist = async (productId: string): Promise<boolean> => {
    const currentStatus = isInWishlist(productId);
    console.log(`WishlistContext: Toggling wishlist for product ${productId}, current status: ${currentStatus}`);

    if (currentStatus) {
      return await removeFromWishlistGlobal(productId);
    } else {
      return await addToWishlistGlobal(productId);
    }
  };

  const value: WishlistContextType = {
    wishlistItems,
    isInWishlist,
    toggleWishlist,
    addToWishlistGlobal,
    removeFromWishlistGlobal,
    refreshWishlistStatus,
  };

  return (
    <WishlistContext.Provider value={value}>
      {children}
    </WishlistContext.Provider>
  );
}

export function useWishlist(): WishlistContextType {
  const context = useContext(WishlistContext);
  if (context === undefined) {
    throw new Error('useWishlist must be used within a WishlistProvider');
  }
  return context;
}
